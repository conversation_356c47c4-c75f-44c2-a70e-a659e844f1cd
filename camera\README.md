# Kamerový modul pre autonómne vozidlo

Tento modul poskytuje funkcionalitu pre detekciu cesty a prekážok pomocou USB kamery.

## Funkcie

### 1. Detekcia cesty
- Rozpoznáva asfaltové/dlážené cesty v parkovom prostredí
- Odlišuje cestu od trávy, stromov a lavičiek
- Vracia offset stredu cesty (-1.0 až 1.0)
- Poskytuje spoľahlivosť detekcie (0.0 až 1.0)

### 2. Detekcia prekážok
- Detekuje pevné prekážky v blízkosti (cca 0.5m)
- Pri detekcii čaká 10 sekúnd
- Následne navrhuje obchádzací manéver

## Použitie

```python
from camera_module import CameraModule

# Inicializácia
camera = CameraModule()
if camera.initialize():
    # Spracovanie snímky
    frame = camera.capture_frame()
    result = camera.process_frame(frame)
    
    print(f"Smer: {result.direction}")
    print(f"Spoľahlivosť: {result.confidence}")
    
    camera.cleanup()
```

## Demo program

```bash
python demo.py
```

Ovládanie:
- `q` - ukončiť
- `s` - uložiť snímku

## Testovanie

```bash
python test_camera.py
```

## Výstupné hodnoty

- `Direction.FORWARD` - jazda dopredu
- `Direction.LEFT` - otočenie doľava  
- `Direction.RIGHT` - otočenie doprava
- `Direction.STOP` - zastavenie (prekážka alebo nízka spoľahlivosť)

## Požiadavky

```bash
pip install opencv-python numpy
```