import cv2
import logging

# Nastavenie logovania
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def list_available_cameras():
    """Zoznam dostupných kamier"""
    logger.info("Hľadám dostupné kamery...")
    
    available_cameras = []
    
    # Testujeme indexy 0-5 (väčšinou stačí)
    for index in range(6):
        cap = cv2.VideoCapture(index)
        if cap.isOpened():
            # Získame informácie o kamere
            width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            fps = cap.get(cv2.CAP_PROP_FPS)
            
            # Pokúsime sa zachytiť snímku na overenie funkčnosti
            ret, frame = cap.read()
            if ret:
                available_cameras.append({
                    'index': index,
                    'width': width,
                    'height': height,
                    'fps': fps,
                    'working': True
                })
                logger.info(f"Kamera {index}: {width}x{height}, {fps} FPS - FUNKČNÁ")
            else:
                logger.warning(f"Kamera {index}: Otvorená ale nečíta snímky")
            
            cap.release()
        else:
            logger.debug(f"Kamera {index}: Nedostupná")
    
    if not available_cameras:
        logger.error("Nenašli sa žiadne funkčné kamery!")
    else:
        logger.info(f"Celkom nájdených kamier: {len(available_cameras)}")
        
    return available_cameras

def test_camera(camera_index):
    """Test konkrétnej kamery"""
    logger.info(f"Testujem kameru s indexom {camera_index}")
    
    cap = cv2.VideoCapture(camera_index)
    if not cap.isOpened():
        logger.error(f"Nemôžem otvoriť kameru {camera_index}")
        return False
    
    # Nastavíme rozlíšenie
    cap.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
    cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)
    
    logger.info("Stlačte 'q' pre ukončenie testu kamery")
    
    try:
        while True:
            ret, frame = cap.read()
            if not ret:
                logger.error("Nepodarilo sa zachytiť snímku")
                break
            
            # Pridáme text s informáciami
            cv2.putText(frame, f"Kamera {camera_index}", (10, 30), 
                       cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
            cv2.putText(frame, "Stlac 'q' pre ukoncenie", (10, 70), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
            
            cv2.imshow(f'Test kamery {camera_index}', frame)
            
            key = cv2.waitKey(1) & 0xFF
            if key == ord('q'):
                break
                
    except KeyboardInterrupt:
        logger.info("Test ukončený")
    finally:
        cap.release()
        cv2.destroyAllWindows()
    
    return True

if __name__ == "__main__":
    print("=== ZOZNAM DOSTUPNÝCH KAMIER ===")
    cameras = list_available_cameras()
    
    if cameras:
        print("\nDostupné kamery:")
        for cam in cameras:
            print(f"  Index {cam['index']}: {cam['width']}x{cam['height']}, {cam['fps']:.1f} FPS")
        
        print("\nChcete otestovať konkrétnu kameru? (zadajte index alebo 'n' pre nie)")
        try:
            user_input = input("Index kamery: ").strip()
            if user_input.lower() != 'n' and user_input.isdigit():
                camera_index = int(user_input)
                if any(cam['index'] == camera_index for cam in cameras):
                    test_camera(camera_index)
                else:
                    print(f"Kamera s indexom {camera_index} nie je dostupná")
        except KeyboardInterrupt:
            print("\nUkončené")
