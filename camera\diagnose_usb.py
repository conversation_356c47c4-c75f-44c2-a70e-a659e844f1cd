import cv2
import time
import threading

def test_camera_with_timeout(camera_index, timeout=10):
    """Test kamery s timeoutom"""
    print(f"Testujem kameru {camera_index} s timeoutom {timeout}s...")
    
    result = {'success': False, 'cap': None, 'error': None}
    
    def open_camera():
        try:
            print(f"Pokúšam sa otvoriť kameru {camera_index}...")
            cap = cv2.VideoCapture(camera_index)
            print(f"VideoCapture vytvorený pre kameru {camera_index}")
            
            if cap.isOpened():
                print(f"Kamera {camera_index} je otvorená")
                result['success'] = True
                result['cap'] = cap
            else:
                print(f"Kamera {camera_index} sa nepodarilo otvoriť")
                result['error'] = "Camera not opened"
                if cap:
                    cap.release()
        except Exception as e:
            print(f"Chyba pri otváraní kamery {camera_index}: {e}")
            result['error'] = str(e)
    
    # Spustíme otvorenie kamery v separátnom vlákne
    thread = threading.Thread(target=open_camera)
    thread.daemon = True
    thread.start()
    
    # Čakáme na dokončenie alebo timeout
    thread.join(timeout)
    
    if thread.is_alive():
        print(f"TIMEOUT: Otvorenie kamery {camera_index} trvalo viac ako {timeout}s")
        return None, "Timeout"
    
    if result['success']:
        print(f"Kamera {camera_index} úspešne otvorená!")
        return result['cap'], None
    else:
        print(f"Nepodarilo sa otvoriť kameru {camera_index}: {result['error']}")
        return None, result['error']

def main():
    print("=== DIAGNOSTIKA USB KAMERY ===")
    
    # Test kamery 0 (vstavaná)
    print("\n1. Testovanie vstavanej kamery (index 0):")
    cap0, error0 = test_camera_with_timeout(0, 5)
    if cap0:
        print("✓ Vstavaná kamera funguje")
        cap0.release()
    else:
        print(f"✗ Vstavaná kamera nefunguje: {error0}")
    
    # Test kamery 1 (USB)
    print("\n2. Testovanie USB kamery (index 1):")
    cap1, error1 = test_camera_with_timeout(1, 15)  # Dlhší timeout pre USB
    if cap1:
        print("✓ USB kamera funguje")
        
        # Skúsime zachytiť snímku
        print("Pokúšam sa zachytiť snímku...")
        ret, frame = cap1.read()
        if ret:
            print("✓ Snímka úspešne zachytená")
            print(f"Rozlíšenie snímky: {frame.shape[1]}x{frame.shape[0]}")
        else:
            print("✗ Nepodarilo sa zachytiť snímku")
        
        cap1.release()
    else:
        print(f"✗ USB kamera nefunguje: {error1}")
    
    # Test s rôznymi backend-mi pre USB kameru
    print("\n3. Testovanie USB kamery s rôznymi backend-mi:")
    backends = [
        (cv2.CAP_DSHOW, "DirectShow"),
        (cv2.CAP_MSMF, "Media Foundation"),
        (cv2.CAP_V4L2, "Video4Linux2"),
    ]
    
    for backend, name in backends:
        try:
            print(f"Testujem {name} backend...")
            cap = cv2.VideoCapture(1, backend)
            if cap.isOpened():
                ret, frame = cap.read()
                if ret:
                    print(f"✓ {name} backend funguje!")
                    cap.release()
                    break
                else:
                    print(f"✗ {name} backend - kamera otvorená ale nečíta snímky")
            else:
                print(f"✗ {name} backend - kamera sa neotvorila")
            cap.release()
        except Exception as e:
            print(f"✗ {name} backend - chyba: {e}")
    
    print("\n=== DIAGNOSTIKA DOKONČENÁ ===")

if __name__ == "__main__":
    main()
