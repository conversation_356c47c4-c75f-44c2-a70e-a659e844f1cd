import cv2
import time

def test_usb_camera():
    """Jednoduchý test USB kamery"""
    print("Testujem USB kameru (index 1)...")
    
    # Pokúsime sa otvoriť kameru
    cap = cv2.VideoCapture(1)
    
    if not cap.isOpened():
        print("CHYBA: Nemôžem otvoriť USB kameru s indexom 1")
        return False
    
    print("USB kamera úspešne otvorená!")
    
    # Nastavíme rozlíšenie
    cap.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
    cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)
    
    # <PERSON>ískame informácie o kamere
    width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    fps = cap.get(cv2.CAP_PROP_FPS)
    
    print(f"Rozlíšenie: {width}x{height}")
    print(f"FPS: {fps}")
    print("Stlačte 'q' pre ukončenie")
    
    frame_count = 0
    start_time = time.time()
    
    try:
        while True:
            ret, frame = cap.read()
            
            if not ret:
                print("CHYBA: Nemôžem načítať snímku z kamery")
                break
            
            # Pridáme informácie na snímku
            cv2.putText(frame, "USB Kamera (Index 1)", (10, 30), 
                       cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
            cv2.putText(frame, f"Rozlisenie: {width}x{height}", (10, 70), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
            
            # Počítanie FPS
            frame_count += 1
            if frame_count % 30 == 0:
                elapsed = time.time() - start_time
                current_fps = frame_count / elapsed
                print(f"Aktuálne FPS: {current_fps:.1f}")
            
            # Zobrazenie snímky
            cv2.imshow('USB Kamera Test', frame)
            
            # Ukončenie na 'q'
            key = cv2.waitKey(1) & 0xFF
            if key == ord('q'):
                break
                
    except KeyboardInterrupt:
        print("\nTest ukončený")
    finally:
        cap.release()
        cv2.destroyAllWindows()
        print("Kamera uvoľnená")
    
    return True

if __name__ == "__main__":
    test_usb_camera()
