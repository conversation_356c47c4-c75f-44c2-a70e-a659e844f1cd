import cv2
import numpy as np
import logging
from camera_module import CameraModule, Direction
import time

# Nastavenie logovania
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class CameraDemo:
    def __init__(self, camera_index=0):  # Predvolene vstavaná kamera (index 0)
        self.camera = CameraModule(camera_index=camera_index)
        self.running = False
        
    def draw_visualization(self, frame: np.ndarray, result) -> np.ndarray:
        """Kreslenie vizualizácie na snímku"""
        vis_frame = frame.copy()
        height, width = vis_frame.shape[:2]
        
        # <PERSON><PERSON><PERSON>ie stredu snímky
        center_x = width // 2
        cv2.line(vis_frame, (center_x, 0), (center_x, height), (0, 255, 255), 2)
        
        # Kreslenie offsetu cesty
        if result.path_center_offset != 0:
            path_x = int(center_x + result.path_center_offset * (width // 2))
            cv2.line(vis_frame, (path_x, 0), (path_x, height), (0, 255, 0), 3)
            
            # Šípka ukazujúca smer
            arrow_y = height // 2
            if result.direction == Direction.LEFT:
                cv2.arrowedLine(vis_frame, (center_x, arrow_y), (center_x - 50, arrow_y), (255, 0, 0), 5)
            elif result.direction == Direction.RIGHT:
                cv2.arrowedLine(vis_frame, (center_x, arrow_y), (center_x + 50, arrow_y), (255, 0, 0), 5)
            elif result.direction == Direction.FORWARD:
                cv2.arrowedLine(vis_frame, (center_x, arrow_y), (center_x, arrow_y - 50), (0, 255, 0), 5)
        
        # Kreslenie ROI pre detekciu prekážok
        roi_y_start = height // 2
        roi_x_start = width // 4
        roi_x_end = 3 * width // 4
        cv2.rectangle(vis_frame, (roi_x_start, roi_y_start), (roi_x_end, height), (255, 255, 0), 2)
        
        # Textové informácie
        info_texts = [
            f"Smer: {result.direction.value.upper()}",
            f"Spoľahlivosť: {result.confidence:.2f}",
            f"Offset cesty: {result.path_center_offset:.2f}",
            f"Prekážka: {'ÁNO' if result.obstacle_detected else 'NIE'}"
        ]
        
        y_offset = 30
        for i, text in enumerate(info_texts):
            color = (0, 0, 255) if result.direction == Direction.STOP else (0, 255, 0)
            cv2.putText(vis_frame, text, (10, y_offset + i * 30), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, color, 2)
        
        # Farebný indikátor smeru v rohu
        indicator_color = {
            Direction.FORWARD: (0, 255, 0),
            Direction.LEFT: (255, 0, 0),
            Direction.RIGHT: (0, 0, 255),
            Direction.STOP: (0, 0, 0)
        }
        
        cv2.circle(vis_frame, (width - 50, 50), 30, indicator_color[result.direction], -1)
        cv2.circle(vis_frame, (width - 50, 50), 30, (255, 255, 255), 2)
        
        return vis_frame
    
    def run_demo(self):
        """Spustenie demo programu"""
        logger.info("Spúšťam demo kamerového modulu")
        
        if not self.camera.initialize():
            logger.error("Nepodarilo sa inicializovať kameru")
            return
        
        self.running = True
        frame_count = 0
        start_time = time.time()
        
        try:
            while self.running:
                # Zachytenie snímky
                frame = self.camera.capture_frame()
                if frame is None:
                    logger.error("Nepodarilo sa zachytiť snímku")
                    break
                
                # Spracovanie snímky
                result = self.camera.process_frame(frame)
                
                # Vytvorenie vizualizácie
                vis_frame = self.draw_visualization(frame, result)
                
                # Zobrazenie
                cv2.imshow('Kamerový modul - Demo', vis_frame)
                
                # Logovanie každých 30 snímok
                frame_count += 1
                if frame_count % 30 == 0:
                    elapsed = time.time() - start_time
                    fps = frame_count / elapsed
                    logger.info(f"FPS: {fps:.1f}, Aktuálny príkaz: {result.direction.value}")
                
                # Ukončenie na stlačenie 'q'
                key = cv2.waitKey(1) & 0xFF
                if key == ord('q'):
                    logger.info("Ukončujem demo na požiadanie používateľa")
                    break
                elif key == ord('s'):
                    # Uloženie snímky
                    filename = f"camera_snapshot_{int(time.time())}.jpg"
                    cv2.imwrite(filename, vis_frame)
                    logger.info(f"Snímka uložená ako {filename}")
                    
        except KeyboardInterrupt:
            logger.info("Demo ukončené cez Ctrl+C")
        except Exception as e:
            logger.error(f"Chyba počas behu demo: {e}")
        finally:
            self.cleanup()
    
    def cleanup(self):
        """Ukončenie demo"""
        self.running = False
        self.camera.cleanup()
        cv2.destroyAllWindows()
        logger.info("Demo ukončené")

def main():
    """Hlavná funkcia"""
    print("=== DEMO KAMEROVÉHO MODULU ===")
    print("Dostupné kamery:")
    print("  0 - Vstavaná kamera notebooku (predvolené)")
    print("  1 - USB webkamera")
    print()
    print("Ovládanie:")
    print("  'q' - ukončiť")
    print("  's' - uložiť snímku")
    print("================================")

    # Možnosť výberu kamery
    try:
        camera_choice = input("Vyberte kameru (0/1) alebo Enter pre predvolenú (0): ").strip()
        if camera_choice == "0" or camera_choice == "":
            camera_index = 0
            print("Používam vstavaná kamera (index 0)")
        elif camera_choice == "1":
            camera_index = 1
            print("Používam USB webkamera (index 1)")
        else:
            print("Neplatný výber, používam predvolenú vstavaná kameru (index 0)")
            camera_index = 0
    except KeyboardInterrupt:
        print("\nUkončené")
        return

    demo = CameraDemo(camera_index=camera_index)
    demo.run_demo()

if __name__ == "__main__":
    main()