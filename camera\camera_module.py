import cv2
import numpy as np
import logging
import time
from enum import Enum
from typing import Tuple, Optional
from dataclasses import dataclass

# Nastavenie logovania
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class Direction(Enum):
    FORWARD = "forward"
    LEFT = "left"
    RIGHT = "right"
    STOP = "stop"

@dataclass
class CameraResult:
    direction: Direction
    confidence: float
    obstacle_detected: bool
    path_center_offset: float  # -1.0 (ľavo) až 1.0 (vpravo)

class CameraModule:
    def __init__(self, camera_index: int = 0, frame_width: int = 640, frame_height: int = 480):
        """
        Inicializácia kamerového modulu
        
        Args:
            camera_index: Index USB kamery (zvyčajne 0)
            frame_width: Šírka snímky
            frame_height: <PERSON><PERSON>š<PERSON> snímky
        """
        self.camera_index = camera_index
        self.frame_width = frame_width
        self.frame_height = frame_height
        self.cap = None
        self.obstacle_detected_time = None
        self.obstacle_wait_duration = 10.0  # 10 sekúnd čakania
        
        logger.info(f"Inicializujem kamerový modul s indexom {camera_index}")
        
    def initialize(self) -> bool:
        """Inicializácia kamery"""
        try:
            self.cap = cv2.VideoCapture(self.camera_index)
            if not self.cap.isOpened():
                logger.error(f"Nemôžem otvoriť kameru s indexom {self.camera_index}")
                return False
                
            self.cap.set(cv2.CAP_PROP_FRAME_WIDTH, self.frame_width)
            self.cap.set(cv2.CAP_PROP_FRAME_HEIGHT, self.frame_height)
            
            logger.info("Kamera úspešne inicializovaná")
            return True
        except Exception as e:
            logger.error(f"Chyba pri inicializácii kamery: {e}")
            return False
    
    def capture_frame(self) -> Optional[np.ndarray]:
        """Zachytenie snímky z kamery"""
        if self.cap is None:
            logger.error("Kamera nie je inicializovaná")
            return None
            
        ret, frame = self.cap.read()
        if not ret:
            logger.error("Nemôžem zachytiť snímku")
            return None
            
        return frame
    
    def detect_path(self, frame: np.ndarray) -> Tuple[float, float]:
        """
        Detekcia cesty a určenie smeru
        
        Returns:
            Tuple[offset, confidence]: offset (-1.0 až 1.0), confidence (0.0 až 1.0)
        """
        # Konverzia do HSV pre lepšiu detekciu farieb
        hsv = cv2.cvtColor(frame, cv2.COLOR_BGR2HSV)
        
        # Maska pre asfalt/dlažbu (tmavé farby)
        lower_path = np.array([0, 0, 0])
        upper_path = np.array([180, 255, 100])
        path_mask = cv2.inRange(hsv, lower_path, upper_path)
        
        # Maska pre trávu/stromy (zelené farby)
        lower_green = np.array([40, 40, 40])
        upper_green = np.array([80, 255, 255])
        green_mask = cv2.inRange(hsv, lower_green, upper_green)
        
        # Invertujeme zelenú masku (cesta nie je zelená)
        path_mask = cv2.bitwise_and(path_mask, cv2.bitwise_not(green_mask))
        
        # Morfologické operácie na vyčistenie masky
        kernel = np.ones((5,5), np.uint8)
        path_mask = cv2.morphologyEx(path_mask, cv2.MORPH_CLOSE, kernel)
        path_mask = cv2.morphologyEx(path_mask, cv2.MORPH_OPEN, kernel)
        
        # Hľadanie kontúr cesty
        contours, _ = cv2.findContours(path_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        if not contours:
            logger.warning("Nenašiel som žiadnu cestu")
            return 0.0, 0.0
        
        # Najväčšia kontúra by mala byť cesta
        largest_contour = max(contours, key=cv2.contourArea)
        
        # Výpočet momentov pre určenie stredu
        M = cv2.moments(largest_contour)
        if M["m00"] == 0:
            return 0.0, 0.0
            
        cx = int(M["m10"] / M["m00"])
        
        # Normalizácia na rozsah -1.0 až 1.0
        frame_center = self.frame_width // 2
        offset = (cx - frame_center) / (self.frame_width // 2)
        offset = max(-1.0, min(1.0, offset))
        
        # Confidence na základe veľkosti kontúry
        area = cv2.contourArea(largest_contour)
        max_area = self.frame_width * self.frame_height * 0.5
        confidence = min(1.0, area / max_area)
        
        logger.debug(f"Cesta detekovaná: offset={offset:.2f}, confidence={confidence:.2f}")
        return offset, confidence
    
    def detect_obstacle(self, frame: np.ndarray) -> bool:
        """
        Detekcia prekážky v blízkosti (cca 0.5m)
        
        Returns:
            bool: True ak je detekovaná prekážka
        """
        # Konverzia do odtieňov šedej
        gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
        
        # Detekcia hrán
        edges = cv2.Canny(gray, 50, 150)
        
        # Definícia oblasti záujmu (spodná polovica snímky, stred)
        height, width = edges.shape
        roi_y_start = height // 2
        roi_x_start = width // 4
        roi_x_end = 3 * width // 4
        
        roi = edges[roi_y_start:height, roi_x_start:roi_x_end]
        
        # Počítanie pixelov hrán v ROI
        edge_pixels = np.sum(roi > 0)
        roi_area = roi.shape[0] * roi.shape[1]
        edge_density = edge_pixels / roi_area
        
        # Prah pre detekciu prekážky
        obstacle_threshold = 0.1
        is_obstacle = edge_density > obstacle_threshold
        
        if is_obstacle:
            logger.warning(f"Prekážka detekovaná! Hustota hrán: {edge_density:.3f}")
        
        return is_obstacle
    
    def process_frame(self, frame: np.ndarray) -> CameraResult:
        """
        Hlavné spracovanie snímky
        
        Returns:
            CameraResult: Výsledok analýzy snímky
        """
        current_time = time.time()
        
        # Detekcia prekážky
        obstacle_detected = self.detect_obstacle(frame)
        
        # Ak je detekovaná prekážka
        if obstacle_detected:
            if self.obstacle_detected_time is None:
                self.obstacle_detected_time = current_time
                logger.info("Prekážka detekovaná - začínam čakanie")
            
            # Čakanie 10 sekúnd
            if current_time - self.obstacle_detected_time < self.obstacle_wait_duration:
                return CameraResult(Direction.STOP, 1.0, True, 0.0)
            else:
                # Po čakaní sa pokúsime o obchádzací manéver
                # Jednoduchá logika - skúsime vpravo
                logger.info("Pokúšam sa o obchádzací manéver - vpravo")
                self.obstacle_detected_time = None
                return CameraResult(Direction.RIGHT, 0.8, True, 0.0)
        else:
            self.obstacle_detected_time = None
        
        # Detekcia cesty
        path_offset, path_confidence = self.detect_path(frame)
        
        # Určenie smeru na základe offsetu cesty
        if path_confidence < 0.3:
            direction = Direction.STOP
            logger.warning("Nízka spoľahlivosť detekcie cesty")
        elif abs(path_offset) < 0.2:
            direction = Direction.FORWARD
        elif path_offset < -0.2:
            direction = Direction.LEFT
        else:
            direction = Direction.RIGHT
        
        return CameraResult(direction, path_confidence, False, path_offset)
    
    def cleanup(self):
        """Ukončenie práce s kamerou"""
        if self.cap is not None:
            self.cap.release()
            logger.info("Kamera uvoľnená")