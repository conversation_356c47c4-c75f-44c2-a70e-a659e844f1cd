#!/usr/bin/env python3
"""
Test script pre kamerový modul
"""

import unittest
import numpy as np
from camera_module import CameraModule, Direction, CameraResult

class TestCameraModule(unittest.TestCase):
    
    def setUp(self):
        self.camera = CameraModule()
    
    def test_initialization(self):
        """Test inicializácie modulu"""
        self.assertIsNotNone(self.camera)
        self.assertEqual(self.camera.frame_width, 640)
        self.assertEqual(self.camera.frame_height, 480)
    
    def test_path_detection_with_mock_frame(self):
        """Test detekcie cesty s mock snímkou"""
        # Vytvorenie mock snímky s cestou v strede
        frame = np.zeros((480, 640, 3), dtype=np.uint8)
        # Tmavá cesta v strede
        frame[200:400, 250:390] = [50, 50, 50]  # Tmavá farba pre cestu
        # Zelená tráva po stranách
        frame[200:400, 0:250] = [0, 100, 0]     # Zelená ľavo
        frame[200:400, 390:640] = [0, 100, 0]   # <PERSON>elená vpravo
        
        offset, confidence = self.camera.detect_path(frame)
        
        # Cesta by mala byť približne v strede
        self.assertAlmostEqual(offset, 0.0, delta=0.2)
        self.assertGreater(confidence, 0.0)
    
    def test_obstacle_detection(self):
        """Test detekcie prekážky"""
        # Mock snímka bez prekážky
        frame_clear = np.zeros((480, 640, 3), dtype=np.uint8)
        self.assertFalse(self.camera.detect_obstacle(frame_clear))
        
        # Mock snímka s prekážkou (veľa hrán)
        frame_obstacle = np.random.randint(0, 255, (480, 640, 3), dtype=np.uint8)
        # Pridáme výrazné hrany v ROI
        frame_obstacle[300:400, 200:400] = 255
        frame_obstacle[301:399, 201:399] = 0
        
        # Toto by malo detekovať prekážku
        result = self.camera.detect_obstacle(frame_obstacle)
        # Môže byť True alebo False v závislosti od náhodných dát
        self.assertIsInstance(result, bool)

if __name__ == '__main__':
    print("Spúšťam testy kamerového modulu...")
    unittest.main()